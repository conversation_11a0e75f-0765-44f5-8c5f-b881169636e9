import socket
import json
import os
import time


class ClientNetwork:
    def __init__(self):
        self.socket = None
        self.connected = False
        self.buffer_size = 4096

    def connect(self, host, port):
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((host, port))
            self.connected = True
            return True
        except Exception as e:
            print(f"Connection error: {e}")
            self.connected = False
            return False

    def disconnect(self):
        if self.socket:
            self.socket.close()
            self.connected = False

    def send_command(self, command, data=None):
        if not self.connected:
            return None

        message = {"command": command}
        if data:
            message["data"] = data

        try:
            self.socket.sendall(json.dumps(message).encode() + b"\n")
            response = self.socket.recv(self.buffer_size)
            return json.loads(response.decode())
        except Exception as e:
            print(f"Command error: {e}")
            return None

    def get_file_list(self):
        response = self.send_command("LIST")
        if response and response.get("status") == "success":
            return response.get("files", [])
        return []

    def download_file(self, file_name, save_path):
        response = self.send_command("DOWNLOAD", {"filename": file_name})
        if response and response.get("status") == "success":
            file_size = response.get("size", 0)

            try:
                with open(save_path, "wb") as f:
                    bytes_received = 0
                    while bytes_received < file_size:
                        data = self.socket.recv(min(self.buffer_size, file_size - bytes_received))
                        if not data:
                            break
                        f.write(data)
                        bytes_received += len(data)
                return True
            except Exception as e:
                print(f"Download error: {e}")
                return False
        return False

    def upload_file(self, file_path):
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)

        response = self.send_command("UPLOAD", {"filename": file_name, "size": file_size})

        if response and response.get("status") == "ready":
            try:
                with open(file_path, "rb") as f:
                    bytes_sent = 0
                    while bytes_sent < file_size:
                        data = f.read(self.buffer_size)
                        if not data:
                            break
                        self.socket.sendall(data)
                        bytes_sent += len(data)

                # Get upload confirmation
                response = json.loads(self.socket.recv(self.buffer_size).decode())
                return response.get("status") == "success"
            except Exception as e:
                print(f"Upload error: {e}")
                return False
        return False
