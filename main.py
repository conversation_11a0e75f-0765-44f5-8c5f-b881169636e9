import tkinter as tk
from tkinter import ttk
import sys
from client.client_gui import ClientApp
from server.server_gui import ServerApp


def main():
    root = tk.Tk()
    root.title("File Share Application")
    root.geometry("800x600")

    # Apply modern theme
    style = ttk.Style()
    try:
        style.theme_use("clam")  # Use a more modern theme if available
    except tk.TclError:
        pass  # Fallback to default theme

    # Configure colors for a modern look
    style.configure("TFrame", background="#f5f5f5")
    style.configure("TButton", background="#4a7abc", foreground="white", font=("Helvetica", 10))
    style.configure("TLabel", background="#f5f5f5", font=("Helvetica", 10))

    # Create a notebook for switching between client and server
    notebook = ttk.Notebook(root)
    notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Add client and server tabs
    client_frame = ttk.Frame(notebook)
    server_frame = ttk.Frame(notebook)

    notebook.add(client_frame, text="Client Mode")
    notebook.add(server_frame, text="Server Mode")

    # Initialize the applications
    client_app = ClientApp(client_frame)
    server_app = ServerApp(server_frame)

    root.mainloop()


if __name__ == "__main__":
    main()
