import socket
import threading
import json
import os
import time
import datetime


class FileServer:
    def __init__(self, shared_dir, port=9000, log_callback=None, client_update_callback=None):
        self.shared_dir = shared_dir
        self.port = port
        self.log_callback = log_callback
        self.client_update_callback = client_update_callback
        self.running = False
        self.server_socket = None
        self.clients = []
        self.client_addresses = []
        self.buffer_size = 4096

    def log(self, message):
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def update_clients_list(self):
        if self.client_update_callback:
            self.client_update_callback(self.client_addresses)

    def start(self):
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(("0.0.0.0", self.port))
            self.server_socket.listen(5)
            self.running = True

            self.log(f"Server started on port {self.port}")

            while self.running:
                try:
                    client_socket, addr = self.server_socket.accept()
                    client_thread = threading.Thread(target=self.handle_client, args=(client_socket, addr))
                    client_thread.daemon = True
                    client_thread.start()

                    self.clients.append(client_socket)
                    self.client_addresses.append(f"{addr[0]}:{addr[1]}")
                    self.update_clients_list()

                    self.log(f"New connection from {addr[0]}:{addr[1]}")
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.running:
                        self.log(f"Error accepting connection: {e}")

        except Exception as e:
            self.log(f"Server error: {e}")
        finally:
            self.stop()

    def stop(self):
        self.running = False

        # Close all client connections
        for client in self.clients:
            try:
                client.close()
            except:
                pass

        # Close server socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass

        self.clients = []
        self.client_addresses = []
        self.update_clients_list()
        self.log("Server stopped")

    def handle_client(self, client_socket, addr):
        try:
            while self.running:
                data = client_socket.recv(self.buffer_size)
                if not data:
                    break

                try:
                    message = json.loads(data.decode())
                    command = message.get("command")

                    if command == "LIST":
                        self.handle_list(client_socket)
                    elif command == "DOWNLOAD":
                        self.handle_download(client_socket, message.get("data", {}))
                    elif command == "UPLOAD":
                        self.handle_upload(client_socket, message.get("data", {}))
                    else:
                        response = {"status": "error", "message": "Unknown command"}
                        client_socket.sendall(json.dumps(response).encode() + b"\n")

                except json.JSONDecodeError:
                    response = {"status": "error", "message": "Invalid JSON format"}
                    client_socket.sendall(json.dumps(response).encode() + b"\n")

        except Exception as e:
            self.log(f"Error handling client {addr[0]}:{addr[1]}: {e}")

        finally:
            # Clean up
            try:
                client_socket.close()
            except:
                pass

            if client_socket in self.clients:
                self.clients.remove(client_socket)

            client_addr = f"{addr[0]}:{addr[1]}"
            if client_addr in self.client_addresses:
                self.client_addresses.remove(client_addr)

            self.update_clients_list()
            self.log(f"Connection closed: {addr[0]}:{addr[1]}")

    def handle_list(self, client_socket):
        try:
            files = []
            for item in os.listdir(self.shared_dir):
                item_path = os.path.join(self.shared_dir, item)
                if os.path.isfile(item_path):
                    stats = os.stat(item_path)
                    files.append(
                        {
                            "name": item,
                            "size": self.format_size(stats.st_size),
                            "modified": datetime.datetime.fromtimestamp(stats.st_mtime).strftime(
                                "%Y-%m-%d %H:%M:%S"
                            ),
                        }
                    )

            response = {"status": "success", "files": files}
            client_socket.sendall(json.dumps(response).encode() + b"\n")

        except Exception as e:
            response = {"status": "error", "message": str(e)}
            client_socket.sendall(json.dumps(response).encode() + b"\n")

    def handle_download(self, client_socket, data):
        filename = data.get("filename")
        if not filename:
            response = {"status": "error", "message": "Filename not provided"}
            client_socket.sendall(json.dumps(response).encode() + b"\n")
            return

        file_path = os.path.join(self.shared_dir, filename)

        if not os.path.isfile(file_path):
            response = {"status": "error", "message": "File not found"}
            client_socket.sendall(json.dumps(response).encode() + b"\n")
            return

        try:
            file_size = os.path.getsize(file_path)
            response = {"status": "success", "size": file_size}
            client_socket.sendall(json.dumps(response).encode() + b"\n")

            # Send the file
            with open(file_path, "rb") as f:
                bytes_sent = 0
                while bytes_sent < file_size:
                    data = f.read(self.buffer_size)
                    if not data:
                        break
                    client_socket.sendall(data)
                    bytes_sent += len(data)

            self.log(f"File '{filename}' downloaded by {client_socket.getpeername()[0]}")

        except Exception as e:
            response = {"status": "error", "message": str(e)}
            client_socket.sendall(json.dumps(response).encode() + b"\n")

    def handle_upload(self, client_socket, data):
        filename = data.get("filename")
        file_size = data.get("size", 0)

        if not filename:
            response = {"status": "error", "message": "Filename not provided"}
            client_socket.sendall(json.dumps(response).encode() + b"\n")
            return

        file_path = os.path.join(self.shared_dir, filename)

        # Tell client we're ready to receive
        response = {"status": "ready"}
        client_socket.sendall(json.dumps(response).encode() + b"\n")

        try:
            with open(file_path, "wb") as f:
                bytes_received = 0
                while bytes_received < file_size:
                    data = client_socket.recv(min(self.buffer_size, file_size - bytes_received))
                    if not data:
                        break
                    f.write(data)
                    bytes_received += len(data)

            self.log(f"File '{filename}' uploaded by {client_socket.getpeername()[0]}")

            # Send success response
            response = {"status": "success"}
            client_socket.sendall(json.dumps(response).encode() + b"\n")

        except Exception as e:
            response = {"status": "error", "message": str(e)}
            client_socket.sendall(json.dumps(response).encode() + b"\n")

    def format_size(self, size):
        # Convert bytes to human-readable format
        for unit in ["B", "KB", "MB", "GB", "TB"]:
            if size < 1024.0:
                return f"{size:.2f} {unit}"
            size /= 1024.0
        return f"{size:.2f} PB"
