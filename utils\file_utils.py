import os
import shutil
import zipfile
import datetime


def get_file_info(file_path):
    """Get information about a file"""
    if not os.path.exists(file_path):
        return None

    stats = os.stat(file_path)
    return {
        "name": os.path.basename(file_path),
        "path": file_path,
        "size": stats.st_size,
        "size_formatted": format_size(stats.st_size),
        "modified": datetime.datetime.fromtimestamp(stats.st_mtime).strftime("%Y-%m-%d %H:%M:%S"),
        "is_dir": os.path.isdir(file_path),
    }


def format_size(size):
    """Convert bytes to human-readable format"""
    for unit in ["B", "KB", "MB", "GB", "TB"]:
        if size < 1024.0:
            return f"{size:.2f} {unit}"
        size /= 1024.0
    return f"{size:.2f} PB"


def zip_directory(directory_path, output_path):
    """Compress a directory into a zip file"""
    with zipfile.ZipFile(output_path, "w", zipfile.ZIP_DEFLATED) as zipf:
        for root, _, files in os.walk(directory_path):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, os.path.dirname(directory_path))
                zipf.write(file_path, arcname)
    return output_path


def unzip_file(zip_path, extract_to):
    """Extract a zip file to the specified directory"""
    with zipfile.ZipFile(zip_path, "r") as zipf:
        zipf.extractall(extract_to)
    return extract_to


def copy_file(src, dst):
    """Copy a file from source to destination"""
    try:
        shutil.copy2(src, dst)
        return True
    except Exception as e:
        print(f"Error copying file: {e}")
        return False


def move_file(src, dst):
    """Move a file from source to destination"""
    try:
        shutil.move(src, dst)
        return True
    except Exception as e:
        print(f"Error moving file: {e}")
        return False


def create_directory(path):
    """Create a directory if it doesn't exist"""
    if not os.path.exists(path):
        os.makedirs(path)
        return True
    return False


def delete_file(path):
    """Delete a file or directory"""
    try:
        if os.path.isdir(path):
            shutil.rmtree(path)
        else:
            os.remove(path)
        return True
    except Exception as e:
        print(f"Error deleting: {e}")
        return False
