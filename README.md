# File Sharing Application

A desktop application built with Python and Tkinter that allows users to share files across multiple systems over a network.

## Features

- Modern UI with ttk styling
- Client-server architecture
- File upload and download capabilities
- Directory sharing
- Real-time server logs
- Connected clients tracking

## Requirements

- Python 3.6+
- Tkinter (usually comes with Python)

## Installation

1. Clone this repository:

   ```
   git clone https://github.com/yourusername/file-share-app.git
   cd file-share-app
   ```

2. Create a virtual environment (optional but recommended):

   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies (if any):
   ```
   pip install -r requirements.txt
   ```

## Usage

Run the application:

```
python main.py
```

### Server Mode

1. Select the "Server Mode" tab
2. Choose a port number (default: 9000)
3. Select a directory to share
4. Click "Start Server"
5. Monitor connections and activities in the log

### Client Mode

1. Select the "Client Mode" tab
2. Enter the server's IP address and port
3. Click "Connect"
4. Browse, download, and upload files

## Network Configuration

For local network sharing:

- Make sure both computers are on the same network
- Use the server's local IP address (e.g., 192.168.1.x)
- Ensure the chosen port is not blocked by firewall

## License

MIT
