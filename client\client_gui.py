import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from .client_network import ClientNetwork


class ClientApp:
    def __init__(self, parent):
        self.parent = parent
        self.client_network = ClientNetwork()
        self.setup_ui()

    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Connection frame
        conn_frame = ttk.LabelFrame(main_frame, text="Server Connection")
        conn_frame.pack(fill=tk.X, padx=5, pady=5)

        # Server address input
        ttk.Label(conn_frame, text="Server IP:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.server_ip = ttk.Entry(conn_frame, width=15)
        self.server_ip.insert(0, "127.0.0.1")
        self.server_ip.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        ttk.Label(conn_frame, text="Port:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.server_port = ttk.Entry(conn_frame, width=6)
        self.server_port.insert(0, "9000")
        self.server_port.grid(row=0, column=3, padx=5, pady=5, sticky=tk.W)

        self.connect_btn = ttk.Button(conn_frame, text="Connect", command=self.connect_to_server)
        self.connect_btn.grid(row=0, column=4, padx=5, pady=5)

        # File browser frame
        browser_frame = ttk.LabelFrame(main_frame, text="Remote Files")
        browser_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for file listing
        self.file_tree = ttk.Treeview(browser_frame, columns=("size", "modified"), selectmode="browse")
        self.file_tree.heading("#0", text="Name")
        self.file_tree.heading("size", text="Size")
        self.file_tree.heading("modified", text="Modified")
        self.file_tree.column("#0", width=300)
        self.file_tree.column("size", width=100)
        self.file_tree.column("modified", width=150)

        # Add scrollbars
        vsb = ttk.Scrollbar(browser_frame, orient="vertical", command=self.file_tree.yview)
        hsb = ttk.Scrollbar(browser_frame, orient="horizontal", command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)

        # Grid layout for treeview and scrollbars
        self.file_tree.grid(row=0, column=0, sticky="nsew")
        vsb.grid(row=0, column=1, sticky="ns")
        hsb.grid(row=1, column=0, sticky="ew")

        browser_frame.grid_rowconfigure(0, weight=1)
        browser_frame.grid_columnconfigure(0, weight=1)

        # Action buttons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)

        self.download_btn = ttk.Button(btn_frame, text="Download", command=self.download_file)
        self.download_btn.pack(side=tk.LEFT, padx=5)

        self.upload_btn = ttk.Button(btn_frame, text="Upload", command=self.upload_file)
        self.upload_btn.pack(side=tk.LEFT, padx=5)

        self.refresh_btn = ttk.Button(btn_frame, text="Refresh", command=self.refresh_files)
        self.refresh_btn.pack(side=tk.LEFT, padx=5)

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Not connected")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)

    def connect_to_server(self):
        ip = self.server_ip.get()
        try:
            port = int(self.server_port.get())
            if self.client_network.connect(ip, port):
                self.status_var.set(f"Connected to {ip}:{port}")
                self.refresh_files()
            else:
                messagebox.showerror("Connection Error", "Failed to connect to server")
        except ValueError:
            messagebox.showerror("Input Error", "Port must be a number")

    def refresh_files(self):
        # Clear existing items
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # Get file list from server
        files = self.client_network.get_file_list()
        if files:
            for file_info in files:
                self.file_tree.insert(
                    "", "end", text=file_info["name"], values=(file_info["size"], file_info["modified"])
                )
            self.status_var.set("File list updated")
        else:
            self.status_var.set("Failed to get file list or no files available")

    def download_file(self):
        selected = self.file_tree.selection()
        if not selected:
            messagebox.showinfo("Selection", "Please select a file to download")
            return

        file_name = self.file_tree.item(selected, "text")
        save_path = filedialog.asksaveasfilename(defaultextension=".*", initialfile=file_name)
        if save_path:
            if self.client_network.download_file(file_name, save_path):
                self.status_var.set(f"Downloaded {file_name}")
            else:
                messagebox.showerror("Download Error", "Failed to download file")

    def upload_file(self):
        file_path = filedialog.askopenfilename()
        if file_path:
            file_name = os.path.basename(file_path)
            if self.client_network.upload_file(file_path):
                self.status_var.set(f"Uploaded {file_name}")
                self.refresh_files()
            else:
                messagebox.showerror("Upload Error", "Failed to upload file")
