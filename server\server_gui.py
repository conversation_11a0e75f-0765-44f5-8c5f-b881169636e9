import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
from .server_network import FileServer


class ServerApp:
    def __init__(self, parent):
        self.parent = parent
        self.server = None
        self.server_thread = None
        self.setup_ui()

    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Server configuration frame
        config_frame = ttk.LabelFrame(main_frame, text="Server Configuration")
        config_frame.pack(fill=tk.X, padx=5, pady=5)

        # Port configuration
        ttk.Label(config_frame, text="Port:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.port_entry = ttk.Entry(config_frame, width=6)
        self.port_entry.insert(0, "9000")
        self.port_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        # Shared directory
        ttk.Label(config_frame, text="Shared Directory:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.dir_var = tk.StringVar()
        dir_entry = ttk.Entry(config_frame, textvariable=self.dir_var, width=40)
        dir_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky=tk.W + tk.E)

        browse_btn = ttk.Button(config_frame, text="Browse", command=self.browse_directory)
        browse_btn.grid(row=1, column=3, padx=5, pady=5)

        # Server control buttons
        self.start_btn = ttk.Button(config_frame, text="Start Server", command=self.start_server)
        self.start_btn.grid(row=2, column=0, padx=5, pady=5)

        self.stop_btn = ttk.Button(
            config_frame, text="Stop Server", command=self.stop_server, state=tk.DISABLED
        )
        self.stop_btn.grid(row=2, column=1, padx=5, pady=5)

        # Configure grid weights
        config_frame.columnconfigure(1, weight=1)

        # Log frame
        log_frame = ttk.LabelFrame(main_frame, text="Server Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Log text widget
        self.log_text = tk.Text(log_frame, height=15, width=60, wrap=tk.WORD)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Scrollbar for log
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

        # Connected clients frame
        clients_frame = ttk.LabelFrame(main_frame, text="Connected Clients")
        clients_frame.pack(fill=tk.X, padx=5, pady=5)

        # Clients listbox
        self.clients_listbox = tk.Listbox(clients_frame, height=5)
        self.clients_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Scrollbar for clients
        clients_scrollbar = ttk.Scrollbar(clients_frame, command=self.clients_listbox.yview)
        clients_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.clients_listbox.config(yscrollcommand=clients_scrollbar.set)

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Server not running")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)

    def browse_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.dir_var.set(directory)

    def start_server(self):
        try:
            port = int(self.port_entry.get())
            shared_dir = self.dir_var.get()

            if not shared_dir:
                messagebox.showerror("Configuration Error", "Please select a shared directory")
                return

            if not os.path.isdir(shared_dir):
                messagebox.showerror("Directory Error", "The selected path is not a valid directory")
                return

            # Create server instance
            self.server = FileServer(shared_dir, port, self.log_callback, self.client_update_callback)

            # Start server in a separate thread
            self.server_thread = threading.Thread(target=self.server.start)
            self.server_thread.daemon = True
            self.server_thread.start()

            # Update UI
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            self.port_entry.config(state=tk.DISABLED)
            self.status_var.set(f"Server running on port {port}")

            self.log_text.insert(tk.END, f"Server started on port {port}\n")
            self.log_text.insert(tk.END, f"Sharing directory: {shared_dir}\n")
            self.log_text.see(tk.END)

        except ValueError:
            messagebox.showerror("Input Error", "Port must be a number")
        except Exception as e:
            messagebox.showerror("Server Error", f"Failed to start server: {str(e)}")

    def stop_server(self):
        if self.server:
            self.server.stop()
            self.server = None

            # Update UI
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.port_entry.config(state=tk.NORMAL)
            self.status_var.set("Server stopped")

            self.log_text.insert(tk.END, "Server stopped\n")
            self.log_text.see(tk.END)

            # Clear clients list
            self.clients_listbox.delete(0, tk.END)

    def log_callback(self, message):
        # This will be called from another thread, so we need to use after
        self.parent.after(0, lambda: self._update_log(message))

    def _update_log(self, message):
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)

    def client_update_callback(self, clients):
        # This will be called from another thread
        self.parent.after(0, lambda: self._update_clients(clients))

    def _update_clients(self, clients):
        self.clients_listbox.delete(0, tk.END)
        for client in clients:
            self.clients_listbox.insert(tk.END, client)
